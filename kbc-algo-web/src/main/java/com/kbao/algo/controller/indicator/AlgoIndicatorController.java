package com.kbao.algo.controller.indicator;

import com.alibaba.fastjson.JSONObject;
import com.kbao.algo.indicator.bean.AlgoIndicatorConfigVo;
import com.kbao.algo.indicator.bean.AlgoNodeResVo;
import com.kbao.algo.indicator.bean.ProcedureDelVo;
import com.kbao.algo.indicator.entity.AlgoIndicator;
import com.kbao.algo.indicator.indicator.service.AlgoIndicatorService;
import com.kbao.algo.indicator.indicatorVersion.entity.AlgoIndicatorVersion;
import com.kbao.algo.indicator.indicatorVersion.service.AlgoIndicatorVersionService;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.algo.indicator.bean.AlgoIndicatorReqVo;
import org.checkerframework.checker.units.qual.A;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.pagehelper.PageInfo;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> jie
 * @Description 管理
 * @Date 2024-10-25
*/
@RestController
@RequestMapping("/web/algoindicator")
public class AlgoIndicatorController extends BaseController {

	@Autowired
	private AlgoIndicatorService algoIndicatorService;
	@Autowired
	private AlgoIndicatorVersionService algoIndicatorVersionService;

	@PostMapping("/page")
	@LogAnnotation(module = "管理", action = "查询", desc = "分页查询列表")
	public Result<PageInfo<AlgoIndicator>> page(@RequestBody RequestObjectPage<AlgoIndicatorReqVo> page) {
		PageInfo<AlgoIndicator> algoIndicatorPage = algoIndicatorService.indicatorPage(page);
		return Result.succeed(algoIndicatorPage,ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/save")
	@LogAnnotation(module = "管理", action = "新增", desc = "保存")
	public Result add(@RequestBody AlgoIndicator algoIndicator){
		algoIndicatorService.save(algoIndicator);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/check")
	@LogAnnotation(module = "管理", action = "新增", desc = "保存")
	public Result checkConfig(@RequestBody AlgoIndicatorConfigVo configVo){
		algoIndicatorService.checkConfig(configVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/config")
	@LogAnnotation(module = "管理", action = "新增", desc = "保存")
	public Result config(@RequestBody AlgoIndicatorConfigVo configVo){
		algoIndicatorService.config(configVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/saveDraft")
	@LogAnnotation(module = "管理", action = "新增", desc = "保存草稿")
	public Result saveDraft(@RequestBody AlgoIndicatorConfigVo configVo){
		algoIndicatorVersionService.saveDraft(configVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/delete")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除")
	public Result delete(@RequestBody AlgoIndicatorReqVo reqVo) {
		algoIndicatorService.delete(reqVo.getIndicatorId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/clean")
	@LogAnnotation(module = "管理", action = "查询", desc = "删除数据")
	public Result<List<AlgoNodeResVo>> dropIndicatorTable(@RequestBody AlgoIndicatorReqVo reqVo) {
		algoIndicatorService.dropIndicatorTable(reqVo.getIndicatorCode(), reqVo.getDataSpaceId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

    @PostMapping("/getConfig")
    @LogAnnotation(module = "管理", action = "查询", desc = "查询配置")
    public Result<AlgoIndicatorConfigVo> getConfig(@RequestBody AlgoIndicatorReqVo reqVo) {
        AlgoIndicatorConfigVo indicator = algoIndicatorService.getConfig(reqVo.getIndicatorId());
        return Result.succeed(indicator, ResultStatusEnum.SUCCESS.getMsg());
    }

    @PostMapping("/getJSONConfig")
    @LogAnnotation(module = "管理", action = "查询", desc = "查询配置")
    public Result<JSONObject> getJSONConfig(@RequestBody AlgoIndicatorReqVo reqVo) {
        JSONObject config = algoIndicatorService.getJSONConfigById(reqVo.getIndicatorId());
        return Result.succeed(config, ResultStatusEnum.SUCCESS.getMsg());
    }

	@PostMapping("/versionConfig")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询版本配置")
	public Result<AlgoIndicatorConfigVo> getVersionConfig(@RequestBody AlgoIndicatorVersion reqVo){
		AlgoIndicatorConfigVo indicator = algoIndicatorVersionService.getVersionConfig(reqVo.getId());
		return Result.succeed(indicator, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/versionPage")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询版本配置")
	public Result<PageInfo<AlgoIndicatorVersion>> versionPage(@RequestBody RequestObjectPage<AlgoIndicatorVersion> pageParams){
		PageInfo<AlgoIndicatorVersion> pageInfo = algoIndicatorVersionService.page(pageParams);
		return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/versionUpdate")
	@LogAnnotation(module = "管理", action = "查询", desc = "版本配置更新")
	public Result updateVersion(@RequestBody AlgoIndicatorVersion reqVo){
		algoIndicatorVersionService.updateRemark(reqVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/versionDel")
	@LogAnnotation(module = "管理", action = "查询", desc = "版本配置删除")
	public Result delIndicatorVersion(@RequestBody AlgoIndicatorVersion reqVo){
		algoIndicatorVersionService.delIndicatorVersion(reqVo.getId());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/getNodeList")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询节点列表")
	public Result<List<AlgoNodeResVo>> getNodeList(){
		List<AlgoNodeResVo> list = algoIndicatorService.getNodeList();
		return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/executeCalcIndicator")
	@LogAnnotation(module = "管理", action = "查询", desc = "查询明细")
	public Result<AlgoIndicator> executeCalcIndicator(@RequestBody JSONObject json){
		Integer strategyBatchId = json.getInteger("strategyBatchId");
		String indicatorBizCode = json.getString("indicatorBizCode");
		String isUpdate = json.getString("isUpdate");
		algoIndicatorService.executeCalcIndicator(strategyBatchId, indicatorBizCode, isUpdate, new ArrayList<>());
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}

	@PostMapping("/dropProcedure")
	@LogAnnotation(module = "管理", action = "删除", desc = "删除存储过程")
	public Result dropIndicatorProcedure(@RequestBody ProcedureDelVo reqVo) {
		algoIndicatorService.dropIndicatorProcedure(reqVo);
		return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
	}
}